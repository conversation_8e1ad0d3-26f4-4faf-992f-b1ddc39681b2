local joinMessages = {}
local joinTimes = {}

AddEventHandler('playerConnecting', function(playerName, setKickReason, deferrals)
    local identifiers = {}
    local playerId = source

    for _, identifier in ipairs(GetPlayerIdentifiers(playerId)) do
        if string.find(identifier, "discord:") then
            identifiers.discord = identifier
        elseif string.find(identifier, "steam:") then
            identifiers.steam = identifier
        elseif string.find(identifier, "license:") then
            identifiers.license = identifier
        elseif string.find(identifier, "ip:") then
            identifiers.ip = identifier
        end
    end

    local discordId = identifiers.discord and identifiers.discord:gsub("discord:", "") or nil
    local discordMention = discordId and ("<@%s>"):format(discordId) or "N/A"
    local steamHex = identifiers.steam and identifiers.steam:gsub("steam:", "") or nil
    local steamLink = steamHex and ("[Click Here](https://steamcommunity.com/profiles/%s)"):format(tonumber(steamHex, 16)) or "N/A"
    local steamRaw = identifiers.steam and ("" .. identifiers.steam .. "") or "N/A"

    local message = {
        username = "x",
        embeds = { {
            title = playerName .. " | Connected to the Server",
            description = "**[Player Connecting]**\n" ..
                         "**Name:** " .. playerName,
            color = 3092790,
            fields = {
                {
                    name = "**[Identifiers]**",
                    value = "**Discord ID:** " .. discordMention .. "\n" ..
                            "**Steam ID:** " .. steamRaw .. "\n" ..
                            "**Steam Profile:** " .. steamLink .. "\n" ..
                            "**License:** " .. (identifiers.license and "" .. identifiers.license .. "" or "||N/A||") .. "\n" ..
                            "**IP:** " .. (identifiers.ip and "||" .. identifiers.ip .. "||" or "||N/A||"),
                    inline = false
                }
            },
            footer = {
                text = "Connection Logs - " .. os.date("%a %b %d %X %Y")
            }
        }}
    }

    PerformHttpRequest("https://discord.com/api/webhooks/1361231121538617505/qWxu52IjnD80kNChjzwFj4KlskFivBxnHPFLGxscgF7FjVwmkC-bjOCLRgplJgb4B8Uu", function(err, text, headers)
        if not err and text then
            local resp = json.decode(text)
            if resp and resp.id then
                joinMessages[playerId] = resp.id
            end
        end
    end, 'POST', json.encode(message), {
        ['Content-Type'] = 'application/json'
    })

    deferrals.done()
end)

-- FIX: move session timer here (player has real ID)
AddEventHandler('playerJoining', function()
    local playerId = source
    joinTimes[playerId] = os.time()
end)

AddEventHandler('playerDropped', function(reason)
    local playerId = source
    local playerName = GetPlayerName(playerId) or "Unknown"

    local joinTime = joinTimes[playerId] or os.time()
    local duration = os.time() - joinTime

    local hours = math.floor(duration / 3600)
    local minutes = math.floor((duration % 3600) / 60)
    local seconds = duration % 60
    local sessionTime = string.format("%d:%02d:%02d", hours, minutes, seconds)

    local replyMsgId = joinMessages[playerId]

    local disconnectEmbed = {
        username = "x",
        embeds = { {
            title = playerName .. " | Disconnected",
            description = "**[Player Disconnected]**\n" ..
                         "**Name:** " .. playerName .. "\n" ..
                         "**Reason:** " .. reason .. "\n" ..
                         "**Session Duration:** " .. sessionTime,
            color = 16711680,
            footer = {
                text = "Disconnect Logs - " .. os.date("%a %b %d %X %Y")
            }
        }},
        message_reference = replyMsgId and { message_id = replyMsgId } or nil
    }

    PerformHttpRequest("https://discord.com/api/webhooks/1361231121538617505/qWxu52IjnD80kNChjzwFj4KlskFivBxnHPFLGxscgF7FjVwmkC-bjOCLRgplJgb4B8Uu", function(err, text, headers)
        -- Done
    end, 'POST', json.encode(disconnectEmbed), {
        ['Content-Type'] = 'application/json'
    })

    -- Clean up
    joinMessages[playerId] = nil
    joinTimes[playerId] = nil
end)
