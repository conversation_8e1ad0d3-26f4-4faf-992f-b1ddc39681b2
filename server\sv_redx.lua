local redXEnabled = true

-- CONFIG: List of allowed Discord IDs
local allowedDiscordIDs = {
    "discord:846599992449302549", -- c
    "discord:x"
}

-- Convert list to lookup table
local allowedLookup = {}
for _, id in ipairs(allowedDiscordIDs) do
    allowedLookup[id] = true
end

local function isAllowed(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, id in ipairs(identifiers) do
        if allowedLookup[id] then return true end
    end
    return false
end

RegisterCommand("toggleredx", function(source)
    if source == 0 then
        redXEnabled = not redXEnabled
        print("[RedX] Toggled from console. Now " .. (redXEnabled and "ENABLED" or "DISABLED"))
        TriggerClientEvent("redx:setState", -1, redXEnabled)
        return
    end

    if not isAllowed(source) then
        TriggerClientEvent("chat:addMessage", source, {
            color = {255, 0, 0},
            multiline = false,
            args = {"RedX", "You do not have permission to use this command."}
        })
        return
    end

    redXEnabled = not redXEnabled
    print("[RedX] Toggled by " .. GetPlayerName(source) .. ". Now " .. (redXEnabled and "ENABLED" or "DISABLED"))
    TriggerClientEvent("redx:setState", -1, redXEnabled)

    TriggerClientEvent("chat:addMessage", -1, {
        color = redXEnabled and {0, 255, 0} or {255, 0, 0},
        multiline = false,
        args = {"x", "Red X is now " .. (redXEnabled and "ENABLED" or "DISABLED")}
    })
end)

RegisterNetEvent("redx:requestState", function()
    TriggerClientEvent("redx:setState", source, redXEnabled)
end)
