-- Shared utility functions for X Addons
-- This file contains common functions used across multiple scripts to reduce code duplication

Utils = {}

-- Cache frequently used values
local cachedPlayerPed = nil
local cachedPlayerId = nil
local lastPedUpdate = 0
local PED_CACHE_TIME = 1000 -- Cache for 1 second

-- Get cached player ped (updates every second max)
function Utils.GetCachedPlayerPed()
    local currentTime = GetGameTimer()
    if not cachedPlayerPed or not DoesEntityExist(cachedPlayerPed) or (currentTime - lastPedUpdate) > PED_CACHE_TIME then
        cachedPlayerPed = PlayerPedId()
        cachedPlayerId = PlayerId()
        lastPedUpdate = currentTime
    end
    return cachedPlayerPed
end

-- Get cached player ID
function Utils.GetCachedPlayerId()
    Utils.GetCachedPlayerPed() -- Ensure cache is updated
    return cachedPlayerId
end

-- Optimized distance calculation with early exit
function Utils.GetDistanceToCoords(coords1, coords2, maxDistance)
    if maxDistance then
        -- Quick 2D distance check first (cheaper calculation)
        local dx = coords1.x - coords2.x
        local dy = coords1.y - coords2.y
        local distance2D = math.sqrt(dx * dx + dy * dy)
        
        if distance2D > maxDistance then
            return distance2D -- Return early if 2D distance exceeds max
        end
    end
    
    return #(coords1 - coords2)
end

-- Optimized player iteration with distance filtering
function Utils.GetNearbyPlayers(centerCoords, maxDistance)
    local nearbyPlayers = {}
    local myPlayerId = Utils.GetCachedPlayerId()
    
    for _, player in pairs(GetActivePlayers()) do
        if player ~= myPlayerId then
            local playerPed = GetPlayerPed(player)
            if DoesEntityExist(playerPed) then
                local playerCoords = GetEntityCoords(playerPed, false)
                local distance = Utils.GetDistanceToCoords(centerCoords, playerCoords, maxDistance)
                
                if not maxDistance or distance <= maxDistance then
                    table.insert(nearbyPlayers, {
                        playerId = player,
                        ped = playerPed,
                        coords = playerCoords,
                        distance = distance
                    })
                end
            end
        end
    end
    
    return nearbyPlayers
end

-- Throttled function execution
local throttledFunctions = {}
function Utils.Throttle(functionName, func, delay)
    local currentTime = GetGameTimer()
    
    if not throttledFunctions[functionName] or (currentTime - throttledFunctions[functionName]) >= delay then
        throttledFunctions[functionName] = currentTime
        return func()
    end
    
    return nil
end

-- Dynamic wait based on distance
function Utils.GetDynamicWait(distance)
    if distance <= 5.0 then
        return 0 -- Very close, update every frame
    elseif distance <= 25.0 then
        return 50 -- Close, update every 50ms
    elseif distance <= 100.0 then
        return 200 -- Medium distance, update every 200ms
    else
        return 500 -- Far away, update every 500ms
    end
end

-- Safe entity cleanup
function Utils.SafeDeleteEntity(entity)
    if entity and DoesEntityExist(entity) then
        if IsEntityAVehicle(entity) then
            DeleteVehicle(entity)
        elseif IsEntityAPed(entity) then
            DeletePed(entity)
        else
            DeleteEntity(entity)
        end
        return true
    end
    return false
end

-- Batch entity cleanup
function Utils.CleanupEntities(entities)
    local cleaned = 0
    for i = #entities, 1, -1 do
        if Utils.SafeDeleteEntity(entities[i]) then
            table.remove(entities, i)
            cleaned = cleaned + 1
        end
    end
    return cleaned
end

-- Safe blip removal
function Utils.SafeRemoveBlip(blip)
    if blip and DoesBlipExist(blip) then
        RemoveBlip(blip)
        return true
    end
    return false
end

-- Optimized chat message with color validation
function Utils.SendChatMessage(title, message, color)
    color = color or {255, 255, 255}
    TriggerEvent('chat:addMessage', {
        color = color,
        args = {title, message}
    })
end

-- Performance monitoring (debug only)
local performanceCounters = {}
function Utils.StartPerformanceTimer(name)
    if Config and Config.DebugPerformance then
        performanceCounters[name] = GetGameTimer()
    end
end

function Utils.EndPerformanceTimer(name)
    if Config and Config.DebugPerformance and performanceCounters[name] then
        local elapsed = GetGameTimer() - performanceCounters[name]
        print(string.format("[PERF] %s took %dms", name, elapsed))
        performanceCounters[name] = nil
    end
end

return Utils
