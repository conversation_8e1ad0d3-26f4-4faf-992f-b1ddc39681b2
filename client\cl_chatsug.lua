Citizen.CreateThread(function()
    local suggestions = {
        {command = "/armour", description = "Gives Yourself Armour"},
        {command = "/a", description = "Gives Yourself Armour"},
        {command = "/heal", description = "Heals you"},
        {command = "/h", description = "Heals you"},
        {command = "/lobby", description = "Make & Join Private Lobbies"},
        {command = "/duels", description = "Custom Duels"},
        {command = "/tracking", description = "Vehicle Tracking"},
        {command = "/aim", description = "Aim labs"},
        {command = "/teleport", description = "Teleport Menu"},


    }

    for _, suggestion in ipairs(suggestions) do
        TriggerEvent('chat:addSuggestion', suggestion.command, suggestion.description)
    end
end)
