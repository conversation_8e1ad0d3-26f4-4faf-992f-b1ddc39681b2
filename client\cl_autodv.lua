RegisterNetEvent('x_core:softDV', function()
    for veh in EnumerateVehicles() do
        if not isVehicleOccupied(veh) then
            DeleteEntity(veh)
        end
    end

    TriggerEvent('chat:addMessage', {
        color = {190, 0, 0},
        args = {'Warning', 'All vehicles have been deleted'}
    })
end)

function isVehicleOccupied(veh)
    for seat = -1, 6 do
        if GetPedInVehicleSeat(veh, seat) ~= 0 then
            return true
        end
    end
    return false
end

function EnumerateVehicles()
    return coroutine.wrap(function()
        local handle, veh = FindFirstVehicle()
        if not handle or handle == -1 then return end
        local success
        repeat
            coroutine.yield(veh)
            success, veh = FindNextVehicle(handle)
        until not success
        EndFindVehicle(handle)
    end)
end
