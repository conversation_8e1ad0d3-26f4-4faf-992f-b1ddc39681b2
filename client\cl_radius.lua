local allRadiusZones = {}

local function DrawSolidSphere(center, radius, r, g, b, a)
    local rings = 18
    local sectors = 36

    local vertices = {}

    for ring = 0, rings do
        local lat = math.pi * (ring / rings)
        for sector = 0, sectors do
            local lon = 2 * math.pi * (sector / sectors)
            local x = center.x + radius * math.sin(lat) * math.cos(lon)
            local y = center.y + radius * math.sin(lat) * math.sin(lon)
            local z = center.z + radius * math.cos(lat)
            table.insert(vertices, vector3(x, y, z))
        end
    end

    for ring = 0, rings - 1 do
        for sector = 0, sectors - 1 do
            local cur = ring * (sectors + 1) + sector + 1
            local next = (ring + 1) * (sectors + 1) + sector + 1

            local v1 = vertices[cur]
            local v2 = vertices[cur + 1]
            local v3 = vertices[next + 1]
            local v4 = vertices[next]

            -- Draw two triangles for the quad
            DrawPoly(v1.x, v1.y, v1.z, v2.x, v2.y, v2.z, v3.x, v3.y, v3.z, r, g, b, a)
            DrawPoly(v1.x, v1.y, v1.z, v3.x, v3.y, v3.z, v4.x, v4.y, v4.z, r, g, b, a)
        end
    end
end

local function sendChatMessage(msg)
    TriggerEvent('chat:addMessage', {
        color = {0, 150, 250},
        multiline = true,
        args = {"X", msg}
    })
end

RegisterCommand("radius", function(source, args)
    local radius = tonumber(args[1])
    if not radius or (radius ~= 100 and radius ~= 150 and radius ~= 250 and radius ~= 500) then
        sendChatMessage("Usage: /radius 100 | 150 | 250 | 500")
        return
    end

    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)

    local areaBlip = AddBlipForRadius(pos.x, pos.y, pos.z, radius + 0.0)
    SetBlipHighDetail(areaBlip, true)
    SetBlipColour(areaBlip, 3)
    SetBlipAlpha(areaBlip, 100)
    SetBlipAsShortRange(areaBlip, false)
    SetBlipDisplay(areaBlip, 4)

    local labelBlip = AddBlipForCoord(pos.x, pos.y, pos.z)
    SetBlipSprite(labelBlip, 1)
    SetBlipScale(labelBlip, 0.7)
    SetBlipColour(labelBlip, 3)
    SetBlipAsShortRange(labelBlip, false)
    SetBlipDisplay(labelBlip, 4)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(radius .. "m Sphere Zone")
    EndTextCommandSetBlipName(labelBlip)

    table.insert(allRadiusZones, {
        coords = pos,
        radius = radius,
        areaBlip = areaBlip,
        labelBlip = labelBlip
    })

    sendChatMessage("Created radius of " .. radius .. "m")
end)

RegisterCommand("radiusclearall", function()
    for _, zone in ipairs(allRadiusZones) do
        if DoesBlipExist(zone.areaBlip) then RemoveBlip(zone.areaBlip) end
        if DoesBlipExist(zone.labelBlip) then RemoveBlip(zone.labelBlip) end
    end
    allRadiusZones = {}
    sendChatMessage("All zones cleared.")
end)

-- Add chat suggestions for the /radius command
Citizen.CreateThread(function()
    TriggerEvent('chat:addSuggestion', '/radius', 'Create a radius zone at your position', {
        { name = 'radius', help = '100, 150, 250, 500' }
    })
    TriggerEvent('chat:addSuggestion', '/radiusclearall', 'Remove all radius zones')
end)

CreateThread(function()
    while true do
        Wait(0)
        for _, zone in ipairs(allRadiusZones) do
            DrawSolidSphere(zone.coords, zone.radius, 0, 150, 255, 80)
        end
    end
end)
