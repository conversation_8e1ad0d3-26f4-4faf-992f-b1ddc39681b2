RegisterNetEvent("x_cleanup:run", function(radius)
    local deletedVehicles, deletedPeds = 0, 0
    local playerCoords = GetEntityCoords(PlayerPedId())
    local limitByRadius = (radius and radius > 0)

    -- Delete vehicles
    for _, vehicle in ipairs(GetGamePool("CVehicle")) do
        if DoesEntityExist(vehicle) then
            if not limitByRadius or #(GetEntityCoords(vehicle) - playerCoords) <= radius then
                if not NetworkHasControlOfEntity(vehicle) then
                    NetworkRequestControlOfEntity(vehicle)
                    local timeout = 0
                    while not NetworkHasControlOfEntity(vehicle) and timeout < 100 do
                        Wait(10)
                        timeout += 1
                    end
                end

                if NetworkHasControlOfEntity(vehicle) then
                    SetEntityAsMissionEntity(vehicle, true, true)
                    DeleteEntity(vehicle)
                    if not DoesEntityExist(vehicle) then
                        deletedVehicles += 1
                    end
                end
            end
        end
    end

    -- Delete peds (excluding players)
    for _, ped in ipairs(GetGamePool("CPed")) do
        if DoesEntityExist(ped) and not IsPedAPlayer(ped) then
            if not limitByRadius or #(GetEntityCoords(ped) - playerCoords) <= radius then
                if not NetworkHasControlOfEntity(ped) then
                    NetworkRequestControlOfEntity(ped)
                    local timeout = 0
                    while not NetworkHasControlOfEntity(ped) and timeout < 100 do
                        Wait(10)
                        timeout += 1
                    end
                end

                if NetworkHasControlOfEntity(ped) then
                    SetEntityAsMissionEntity(ped, true, true)
                    DeleteEntity(ped)
                    if not DoesEntityExist(ped) then
                        deletedPeds += 1
                    end
                end
            end
        end
    end

    -- Show result in chat
    TriggerEvent("chat:addMessage", {
        color = {190, 0, 0},
        multiline = true,
        args = {
            "CLEANUP",
            ("Removed %d vehicle(s) and %d ped(s)%s."):format(
                deletedVehicles,
                deletedPeds,
                limitByRadius and (" within %dm radius"):format(radius) or ""
            )
        }
    })
end)
