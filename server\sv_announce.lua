RegisterCommand("announce", function(source, args, rawCommand)
    local src = source
    local msg = table.concat(args, " ")

    if src == 0 or IsPlayerAceAllowed(src, "command.announce") then
        TriggerClientEvent('chat:addMessage', -1, {
            args = { "x:", msg },
            color = { 190, 0, 0 } -- Red color
        })
    else
        TriggerClientEvent('chat:addMessage', src, {
            args = { "Error", "You do not have permission to use this command." },
            color = { 190, 0, 0 }
        })
    end
end, false)
