-- Performance monitoring and optimization commands
-- Only active when Config.DebugPerformance is enabled

if not Config.DebugPerformance then
    return -- Exit early if performance debugging is disabled
end

-- Performance monitoring variables
local performanceStats = {
    frameTime = 0,
    memoryUsage = 0,
    entityCount = 0,
    lastUpdate = 0
}

-- Monitor performance metrics
Citizen.CreateThread(function()
    while Config.DebugPerformance do
        local currentTime = GetGameTimer()
        
        -- Update stats every 5 seconds
        if currentTime - performanceStats.lastUpdate >= 5000 then
            performanceStats.frameTime = GetFrameTime() * 1000 -- Convert to milliseconds
            performanceStats.memoryUsage = collectgarbage("count")
            performanceStats.entityCount = #GetGamePool('CObject') + #GetGamePool('CPed') + #GetGamePool('CVehicle')
            performanceStats.lastUpdate = currentTime
        end
        
        Citizen.Wait(5000) -- Check every 5 seconds
    end
end)

-- Performance stats command
RegisterCommand('xperf', function()
    if not Config.DebugPerformance then
        Utils.SendChatMessage("Performance", "Performance debugging is disabled", {255, 0, 0})
        return
    end
    
    local resourceStats = ResourceManager.GetStats()
    
    local message = string.format(
        "Frame Time: %.2fms | Memory: %.2fMB | Entities: %d | Tracked: %d blips, %d entities",
        performanceStats.frameTime,
        performanceStats.memoryUsage / 1024,
        performanceStats.entityCount,
        resourceStats.blips,
        resourceStats.entities
    )
    
    Utils.SendChatMessage("Performance", message, {0, 255, 255})
end, false)

-- Memory optimization command
RegisterCommand('xoptimize', function()
    local beforeMemory = collectgarbage("count")
    
    -- Run optimization
    ResourceManager.OptimizeMemory()
    
    local afterMemory = collectgarbage("count")
    local saved = beforeMemory - afterMemory
    
    Utils.SendChatMessage("Optimization", string.format("Memory optimized: %.2fKB freed", saved), {0, 255, 0})
end, false)

-- Resource cleanup command
RegisterCommand('xcleanup', function()
    local cleaned = ResourceManager.CleanupAll()
    
    Utils.SendChatMessage("Cleanup", string.format("Cleaned up %d blips and %d entities", cleaned.blips, cleaned.entities), {0, 255, 0})
end, false)

-- Add chat suggestions for performance commands
Citizen.CreateThread(function()
    if Config.DebugPerformance then
        TriggerEvent('chat:addSuggestion', '/xperf', 'Show performance statistics')
        TriggerEvent('chat:addSuggestion', '/xoptimize', 'Optimize memory usage')
        TriggerEvent('chat:addSuggestion', '/xcleanup', 'Clean up tracked resources')
    end
end)
