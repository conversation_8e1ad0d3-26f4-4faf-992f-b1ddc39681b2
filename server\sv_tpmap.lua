-- Teleport to waypoint
RegisterCommand("tpm", function(source, args, rawCommand)
    local src = source
    local playerPed = GetPlayerPed(src)
    local waypoint = GetFirstBlipInfoId(8) -- Waypoint blip ID

    if DoesBlipExist(waypoint) then
        local coord = GetBlipInfoIdCoord(waypoint)
        SetEntityCoords(playerPed, coord.x, coord.y, coord.z, false, false, false, false)
        TriggerClientEvent("chat:addMessage", src, {color = {0, 190, 0}, args = {"Success", "Teleported to waypoint!"}})
    else
        TriggerClientEvent("chat:addMessage", src, {color = {190, 0, 0}, args = {"Error", "No waypoint set!"}})
    end
end, false)

-- Teleport all players to the command sender
RegisterCommand("tpall", function(source, args, rawCommand)
    local src = source
    local playerPed = GetPlayerPed(src)
    local playerCoords = GetEntityCoords(playerPed)
    local offset = 1.5 -- Offset distance to prevent collisions
    local index = 0

    -- Get list of all players
    for _, playerId in ipairs(GetPlayers()) do
        if playerId ~= tostring(src) then
            local targetPed = GetPlayerPed(tonumber(playerId))
            local newCoords = vector3(playerCoords.x + (index * offset), playerCoords.y, playerCoords.z)
            SetEntityCoords(targetPed, newCoords.x, newCoords.y, newCoords.z, false, false, false, false)
            TriggerClientEvent("chat:addMessage", playerId, {color = {0, 0, 190}, args = {"Info", "You have been teleported!"}})
            index = index + 1
        end
    end

    -- Notify the command sender
    TriggerClientEvent("chat:addMessage", src, {color = {0, 190, 0}, args = {"Success", "All players teleported to you!"}})
end, true)