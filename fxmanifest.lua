fx_version 'cerulean'
game 'gta5'
lua54 'yes'

author 'x | 103.1'
description 'X Addons'
version ''

client_scripts {
    'client/*.lua'
}

server_scripts {
    'server/*.lua'
}

escrow_ignore {
    'config.lua'
}


files {
    'locales/*.json',
    'watermark/nui/index.html',
    'watermark/nui/style.css',
    'watermark/nui/logo.png'
}

ui_page 'watermark/nui/index.html'

shared_scripts {
    'config.lua',
    '@ox_lib/init.lua'
}

ox_libs {
    'locale',
    'math'
}