RegisterCommand("resetme", function()
    local playerPed = PlayerPedId()

    -- Revive if dead
    if IsEntityDead(playerPed) then
        local coords = GetEntityCoords(playerPed)
        ResurrectPed(playerPed)
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, false)
        ClearPedTasksImmediately(playerPed)
    end

    -- Cancel animations and ragdoll
    ClearPedTasksImmediately(playerPed)
    ClearPedSecondaryTask(playerPed)
    SetPedCanRagdoll(playerPed, false)
    Wait(100)
    SetPedCanRagdoll(playerPed, true)

    -- Set health to full after revive
    SetEntityHealth(playerPed, 200)

    -- Unfreeze if frozen
    FreezeEntityPosition(playerPed, false)

    -- Kill the player again
    SetEntityHealth(playerPed, 0)  -- This will kill the player immediately
end, false)
