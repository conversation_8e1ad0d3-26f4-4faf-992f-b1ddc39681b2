-- /checkbucket [playerID] - Checks the routing bucket of the specified player or yourself if none given
RegisterCommand("checkbucket", function(source, args, rawCommand)
    local playerId = tonumber(args[1]) or source -- Default to self if no ID is given
    local bucket = GetPlayerRoutingBucket(playerId)

    if bucket then
        TriggerClientEvent('chat:addMessage', source, {
            args = {"^2Player " .. playerId .. " is in bucket " .. bucket}
        })
    else
        TriggerClientEvent('chat:addMessage', source, {
            args = {"^1Error retrieving bucket for player " .. playerId}
        })
    end
end, false)

-- /joinbucket [bucketID] - Joins the specified routing bucket
RegisterCommand("joinbucket", function(source, args, rawCommand)
    local bucketId = tonumber(args[1])
    if not bucketId then
        TriggerClientEvent('chat:addMessage', source, {
            args = {"^1Usage: /joinbucket [bucketID]"}
        })
        return
    end

    SetPlayerRoutingBucket(source, bucketId)
    TriggerClientEvent('chat:addMessage', source, {
        args = {"^2You have joined bucket " .. bucketId}
    })
end, false)

-- /buckets - Lists all current buckets and the player names in each
RegisterCommand("buckets", function(source)
    local buckets = {}

    for _, playerId in ipairs(GetPlayers()) do
        local bucket = GetPlayerRoutingBucket(playerId)
        local name = GetPlayerName(playerId)

        if not buckets[bucket] then
            buckets[bucket] = {}
        end

        table.insert(buckets[bucket], name)
    end

    if next(buckets) == nil then
        TriggerClientEvent('chat:addMessage', source, {
            args = {"^1No players found in any buckets."}
        })
        return
    end

    for bucketId, names in pairs(buckets) do
        local nameList = table.concat(names, ", ")
        TriggerClientEvent('chat:addMessage', source, {
            args = {("^3Bucket %s: ^7%s"):format(bucketId, nameList)}
        })
    end
end, false)
