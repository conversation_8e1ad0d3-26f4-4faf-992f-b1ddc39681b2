local weaponRanges = {
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_PISTOL")] = 25.0,
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_MARKSMANRIFLE")] = 210.0,
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_SCAR")] = 200.0,
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_OC_SCAR")] = 300.0,
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_FAMAS")] = 200.0,
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_scarmk2")] = 215.0,
    [Get<PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_FAMASMK2")] = 215.0,
    [Get<PERSON><PERSON><PERSON><PERSON>("WEAPON_COMBATMG")] = 200.0,
    [Get<PERSON>ash<PERSON><PERSON>("WEAPON_M70")] = 100.0
}

local showingMarker = false
local lastRange = nil -- Store last used range

local function drawMarker(coords, radius)
    DrawMarker(28, coords.x, coords.y, coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, radius, radius, radius, 255, 0, 0, 125, false, true, 2, false, nil, nil, false)
end

local function startRangeMarker(range)
    showingMarker = true
    lastRange = range -- Save the last used range
    CreateThread(function()
        while showingMarker do
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            drawMarker(coords, range)
            Wait(0)
        end
    end)
end

RegisterCommand("range", function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    if not DoesEntityExist(playerPed) then return end

    if showingMarker then
        showingMarker = false
        return
    end

    local weapon = GetSelectedPedWeapon(playerPed)
    local range = weaponRanges[weapon]

    if range == nil then
        TriggerEvent("chat:addMessage", {
            args = {"Error", "Weapon not found in range data."},
            color = {190, 0, 0}
        })
        return
    end

    startRangeMarker(range) -- Start the marker
end, false)

RegisterNetEvent("range")
AddEventHandler("range", function()
    ExecuteCommand("range")
end)

-- Ensure marker stays after dying & reviving
CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        if DoesEntityExist(playerPed) and IsEntityDead(playerPed) then
            showingMarker = false -- Temporarily disable on death
            while IsEntityDead(playerPed) do Wait(100) end -- Wait until revived
            if lastRange then
                startRangeMarker(lastRange) -- Restore the last used range
            end
        end
        Wait(500)
    end
end)
