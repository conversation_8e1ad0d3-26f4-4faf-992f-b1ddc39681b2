local isSpawning = false
local currentRouteIndex = 1
local markerTable = {
    {
        Loc = vector3(-1377.5667, -2945.2593, 12.945),
        TextLoc = vector3(-1377.5667, -2945.2593, 13.9448)
    },
    {
        Loc = vector3(-1376.0751, -2946.2971, 13.9448),
        TextLoc = vector3(-1376.0751, -2946.2971, 13.9448)
    }
}

local spawnEndPoints = {
    {
        Spawn = vector3(-1596.9167, -2798.6243, 13.9610),
        End = vector3(-1208.2678, -3022.7458, 13.9784)
    }
}

local spawnedVehicles = {}
local spawnedPeds = {}
local blip -- Declare global blip variable
local lastActionTime = 0 -- Variable to track the last action time
local lobbyName = ""
local lobbyPassword = ""


Citizen.CreateThread(function()
    CreateBlips()

    while true do
        Citizen.Wait(0)
        local route = markerTable[currentRouteIndex]
        if route then
            local markerCoords = route.Loc
            local textCoords = route.TextLoc
            local playerCoords = GetEntityCoords(PlayerPedId(), false)
            local distance = #(playerCoords - markerCoords)

            -- Draw the 3D world marker
            DrawMarker(27, markerCoords.x, markerCoords.y, markerCoords.z, 0, 0, 0, 0, 0, 0, 1.303, 1.3001, 1.3001, 0, 0, 128, 150, false, 0, 0, false)

            local markerText = isSpawning and "[~g~G~w~] To End" or "[~g~G~w~] To Start"

            if distance <= 1.2 then
                exports.motiontext:Draw3DText({
                    xyz = textCoords,
                    text = {
                        content = markerText,
                        rgb = {255, 255, 255, 255},
                        textOutline = true,
                        scaleMultiplier = 1,
                        font = 4,
                    },
                    perspectiveScale = 4,
                    radius = 10.2
                })

                if IsControlJustPressed(1, 47) then
                    -- Spam protection: check if enough time has passed since the last action
                    local currentTime = GetGameTimer()
                    if currentTime - lastActionTime >= 3000 then -- 3000 ms = 3 seconds
                        if isSpawning then
                            StopSpawning()
                        else
                            StartSpawning()
                        end
                        lastActionTime = currentTime -- Update last action time
                    else
                        TriggerEvent("chat:addMessage", {color = {255, 0, 0}, args = { "Tracking", "Please wait 3 seconds before trying again." } })
                    end
                end
            end

            -- Check if the player is out of the 100m radius
            if #(playerCoords - markerCoords) > 200.0 and isSpawning then
                StopSpawning()
            end
        end
    end
end)

function StartSpawning()
    isSpawning = true
    TriggerEvent("chat:addMessage", {color = {0, 190, 0}, args = { "x", "Starting tracking... Please wait 2 seconds." } })

    -- Request the player's current bucket from the server
    TriggerServerEvent("custom:checkPlayerBucket")
end

-- Server response: If in an existing bucket, leave lobby first
RegisterNetEvent("custom:receivePlayerBucket", function(bucket)
    if bucket ~= 0 then
        TriggerEvent("chat:addMessage", {color = {190, 0, 0}, args = { "x", "Leaving current lobby..." } })
        TriggerServerEvent("custom:leaveLobby")
        Citizen.Wait(1000) -- Give time for the server to process leaving
    end

    -- Proceed with lobby creation
    TriggerEvent("custom:createNewLobby")
end)

RegisterNetEvent("custom:createNewLobby", function()
    Citizen.Wait(2000) -- Wait 2 seconds before creating the lobby

    lobbyName = "tracking"
    lobbyPassword = "tracking" .. math.random(1000, 9999)

    -- Attempt to create the lobby
    TriggerServerEvent("custom:createLobby", lobbyName, lobbyPassword)

    -- Wait 1 second and check if the bucket is still 0 (indicating failure)
    Citizen.Wait(1000)
    TriggerServerEvent("custom:verifyPlayerBucket")
end)

-- Server response: Verify if the lobby creation failed
RegisterNetEvent("custom:verifyBucketResponse", function(bucket)
    if bucket == 0 then
        isSpawning = false
        TriggerEvent("chat:addMessage", {color = {190, 0, 0}, args = { "x", "Tracking can't start in main lobby, please try again." } })
        return
    end

    -- If bucket is valid, show the actual created lobby name and password
    TriggerEvent("chat:addMessage", {color = {37, 29, 171}, args = { "Info", "Lobby Created: " .. lobbyName .. " | Password: " .. lobbyPassword } })

    -- Continue tracking
    Citizen.CreateThread(function()
        while isSpawning do
            SpawnCar()
            Citizen.Wait(5000) -- Delay between car spawns
        end
    end)
end)
function StopSpawning()
    isSpawning = false

    for _, veh in ipairs(spawnedVehicles) do
        if DoesEntityExist(veh) then
            DeleteEntity(veh)
        end
    end
    for _, ped in ipairs(spawnedPeds) do
        if DoesEntityExist(ped) then
            DeleteEntity(ped)
        end
    end

    spawnedVehicles = {}
    spawnedPeds = {}

    -- Trigger leave lobby function
    TriggerServerEvent("custom:leaveLobby")

    -- Notify the user
    TriggerEvent("chat:addMessage", {color = {0, 190, 0}, args = { "x", "Tracking ended." } })
end

function SpawnCar()
    local points = spawnEndPoints[currentRouteIndex]
    local vehicleList = {'vc_mh3650', 'bc', 'gt2017', 'topfoil', 'sunrise1', 'bati', 'r8ppi', 'caracara2'}
    local vehicleType = vehicleList[math.random(#vehicleList)]
    RequestModel(GetHashKey(vehicleType))
    while not HasModelLoaded(GetHashKey(vehicleType)) do
        Citizen.Wait(0)
    end

    local spawnAtEnd = math.random(0, 1) == 1
    local spawnCoords = spawnAtEnd and points.End or points.Spawn
    local endCoords = spawnAtEnd and points.Spawn or points.End
    local heading = spawnAtEnd and 60.0 or 239.34

    local veh = CreateVehicle(GetHashKey(vehicleType), spawnCoords.x, spawnCoords.y, spawnCoords.z, heading, true, true)
    SetModelAsNoLongerNeeded(GetHashKey(vehicleType))

    table.insert(spawnedVehicles, veh)

    local pedModel = GetHashKey("a_m_y_hasjew_01")
    RequestModel(pedModel)
    while not HasModelLoaded(pedModel) do
        Citizen.Wait(0)
    end

    local ped = CreatePedInsideVehicle(veh, 26, pedModel, -1, true, true)
    SetEntityAsMissionEntity(ped, true, true)
    SetPedCombatAttributes(ped, 0, true)
    SetBlockingOfNonTemporaryEvents(ped, true)

    -- Configurable ped health
    local pedHealth = 200 -- Change this value to configure health
    SetEntityMaxHealth(ped, pedHealth)
    SetEntityHealth(ped, pedHealth)

    -- Disable headshot multiplier
    SetPedSuffersCriticalHits(ped, false)

    table.insert(spawnedPeds, ped)

    TaskVehicleDriveToCoord(ped, veh, endCoords.x, endCoords.y, endCoords.z, 88.888, 1.0, **********, **********, 1.0, true)

    Citizen.CreateThread(function()
        while DoesEntityExist(veh) and DoesEntityExist(ped) do
            Citizen.Wait(100)
            if IsPedDeadOrDying(ped, true) then
                DeleteEntity(veh)
                DeleteEntity(ped)
                break
            end

            local vehCoords = GetEntityCoords(veh, false)
            if #(vehCoords - endCoords) <= 5.0 then
                DeleteEntity(veh)
                DeleteEntity(ped)
                break
            end
        end

        -- Additional safeguard to delete ped and vehicle if loop ends unexpectedly
        if DoesEntityExist(ped) then
            DeleteEntity(ped)
        end
        if DoesEntityExist(veh) then
            DeleteEntity(veh)
        end
    end)
end

function CreateBlips()
    -- Remove previous blips if any (optional for cleanup)
    if blip then
        RemoveBlip(blip)
    end

    -- Create a new blip for the first route's location (you can adjust to your preferred route)
    local route = markerTable[1]
    blip = AddBlipForCoord(route.Loc)

    -- Set the blip to a weapon icon (sprite ID 316, a gun icon)
    SetBlipSprite(blip, 150) -- Gun Icon
    SetBlipScale(blip, 0.7) -- Set the scale of the blip on the minimap

    -- Create a radius blip around the blip (optional)
    local radiusBlip = AddBlipForRadius(route.Loc.x, route.Loc.y, route.Loc.z, 50.0) -- 50.0 is the radius size
    SetBlipAlpha(radiusBlip, 100) -- Set transparency of the radius
    SetBlipColour(radiusBlip, 34) -- Optional: set radius color to red (or adjust as needed)

    -- Set the blip name to something descriptive
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Tracking")
    EndTextCommandSetBlipName(blip)
end
