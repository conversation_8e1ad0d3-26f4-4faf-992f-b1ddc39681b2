# Staff Logging System - User Guide

## Overview
The Staff Logging System automatically tracks all player command usage and provides comprehensive monitoring tools for server administrators.

## 🔧 Setup

### 1. Discord ID Configuration
Edit `shared/staff_logger.lua` and add your Discord IDs to the `authorizedDiscordIds` table:

```lua
local authorizedDiscordIds = {
    "846599992449302549", -- cruzy (example)
    "YOUR_DISCORD_ID_HERE",
    "ANOTHER_STAFF_DISCORD_ID",
}
```

### 2. Configuration Options
In `config.lua`, you can adjust:

```lua
Config.StaffLogging = true -- Enable/disable logging
Config.LogRetentionHours = 24 -- How long to keep logs
Config.MaxLogEntries = 500 -- Maximum logs in memory
Config.LogToConsole = true -- Print to server console
```

## 📋 Commands

### `/xlogs` - View Command Logs
Basic usage:
```
/xlogs
```

With filters:
```
/xlogs player:john          -- Filter by player name
/xlogs cmd:heal             -- Filter by command
/xlogs id:123               -- Filter by player ID
/xlogs hours:2              -- Show last 2 hours only
/xlogs limit:20             -- Show up to 20 logs
```

Combined filters:
```
/xlogs player:john cmd:heal hours:1 limit:5
```

### `/xlogstats` - View Statistics
Shows overall logging statistics including:
- Total logs recorded
- Number of unique players
- Recent activity (last hour)
- Most used commands

### `/xclearlogs` - Clean Old Logs
Manually removes logs older than the retention period.

### `/xloghelp` - Show Help
Displays all available commands and usage examples.

## 📊 What Gets Logged

### Player Utility Commands
- `/heal`, `/h` - Player healing
- `/armour`, `/a` - Player armour
- `/resetme` - Player reset

### Teleport Commands
- `/tpm` - Teleport to waypoint
- `/tpall` - Teleport all players
- `/teleport` - Teleport menu
- `/tracking`, `/spawn` - Location teleports

### Admin Commands
- `/announce` - Server announcements
- `/clearall` - Clear chat
- `/cleanup` - Entity cleanup
- `/dv` - Delete vehicles
- `/setped` - Change player model

### Weather/Time Commands
- `/weather` - Change weather
- `/time` - Change time
- `/freezetime` - Freeze time
- `/blackout` - Toggle blackout
- `/morning`, `/noon`, `/evening`, `/night` - Time presets

### Dangerous Commands (Highlighted in Red)
- `/crash` - Crash player
- `/crashall` - Crash all players

### Other Commands
- Greenzone commands (`/setzone`, `/clearzone`)
- Bucket commands (`/joinbucket`, `/checkbucket`)
- Weapon commands (`/scope`, `/extended`, `/grip`)
- Debug commands (`/xperf`, `/xoptimize`)

## 🎨 Log Display Features

### Color Coding
- **Red**: Crash commands (high priority)
- **Green**: Heal/armour commands
- **Blue**: Teleport commands
- **Yellow**: Weather/time commands
- **White**: Other commands

### Information Displayed
Each log entry shows:
- Time ago (in minutes)
- Player name and ID
- Command used
- Command arguments
- Timestamp

### Example Output
```
=== COMMAND LOGS (Total: 45, Recent: 8) ===
[2m ago] John_Doe (123): /heal
[5m ago] Admin_User (456): /weather clear
[8m ago] Player_Name (789): /tpm
[12m ago] John_Doe (123): /armour
```

## 🔍 Filtering Examples

### Find all heal commands in the last hour:
```
/xlogs cmd:heal hours:1
```

### Find all commands by a specific player:
```
/xlogs player:john
```

### Find recent teleport commands:
```
/xlogs cmd:tpm hours:2 limit:10
```

### Find commands by player ID:
```
/xlogs id:123
```

## 🛡️ Security Features

### Permission System
- Only authorized Discord IDs can view logs
- Console access always available
- Permission checks on all log commands

### Data Protection
- Logs automatically expire after 24 hours (configurable)
- Memory limits prevent excessive storage
- Automatic cleanup every hour

### Privacy
- Only command usage is logged, not chat messages
- No sensitive data stored
- Logs are server-local only

## 📈 Performance Impact

### Minimal Overhead
- Lightweight logging system
- Efficient memory management
- Automatic cleanup prevents bloat

### Server Console Integration
All command usage is also printed to server console in real-time:
```
[STAFF-LOG] John_Doe (123) used /heal
[STAFF-LOG] Admin_User (456) used /weather clear
```

## 🔧 Troubleshooting

### "You do not have permission" Error
1. Check your Discord ID is correctly added to `authorizedDiscordIds`
2. Ensure your Discord is linked to your FiveM account
3. Restart the resource after adding your ID

### No Logs Showing
1. Verify `Config.StaffLogging = true`
2. Check if logs have expired (older than retention period)
3. Try `/xlogstats` to see if logging is working

### Performance Issues
1. Reduce `Config.MaxLogEntries` if needed
2. Lower `Config.LogRetentionHours` for less storage
3. Use `/xclearlogs` to manually clean old entries

## 📝 Notes

- Logs are stored in memory only (not persistent across restarts)
- System automatically hooks into existing commands
- Compatible with both native and ox_lib commands
- Real-time logging with immediate console output
- Designed for minimal performance impact

## 🚀 Advanced Usage

### Console Commands
All commands work from server console without permission checks:
```
xlogs
xlogstats
xclearlogs
```

### Integration with Other Systems
The logging system can be extended to track custom commands by adding them to the `trackedCommands` table in `shared/staff_logger.lua`.

This system provides comprehensive oversight of player command usage while maintaining performance and security.
