-- Define allowed admin Discord IDs
local adminDiscords = {
    ["discord:846599992449302549"] = true, -- c
    ["discord:674512215486758962"] = true, --skz
}

-- Get Discord ID from a player
local function getDiscordId(source)
    for _, id in ipairs(GetPlayerIdentifiers(source)) do
        if id:find("discord:") then
            return id
        end
    end
    return nil
end

-- Admin-only /timeout command (console allowed)
RegisterCommand("timeout", function(source, args)
    if source ~= 0 then
        local discord = getDiscordId(source)
        if not discord or not adminDiscords[discord] then
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                multiline = true,
                args = {"x", "You are not authorized to use this command."}
            })
            return
        end
    end

    local targetId = tonumber(args[1])
    if targetId and GetPlayerName(targetId) then
        DropPlayer(targetId, "Timed out.")
    else
        if source ~= 0 then
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 165, 0},
                multiline = true,
                args = {"x", "Invalid player ID."}
            })
        end
    end
end, false)
