-- /tpm Command:
RegisterCommand('tpm', function(source, args, rawCommand)
    local playerPed = GetPlayerPed(-1) -- Get the local player's Ped
    local waypoint = GetFirstBlipInfoId(8) -- Get the waypoint blip (ID 8 is for waypoints)

    if DoesBlipExist(waypoint) then
        local waypointCoords = GetBlipInfoIdCoord(waypoint) -- Get the waypoint's coordinates

        -- Get ground level to avoid spawning in the void
        local _, groundZ = GetGroundZFor_3dCoord(waypointCoords.x, waypointCoords.y, 1000.0, false)
        if groundZ == 0 then groundZ = waypointCoords.z end -- Use waypoint's Z if ground not found

        -- brings vehicle
        if IsPedInAnyVehicle(playerPed, false) then
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            SetEntityCoordsNoOffset(vehicle, waypointCoords.x, waypointCoords.y, groundZ, false, false, false)
        else
            SetEntityCoordsNoOffset(playerPed, waypointCoords.x, waypointCoords.y, groundZ, false, false, false)
        end

        -- Chat confirmation
        TriggerEvent('chat:addMessage', {
            args = {"Teleport", "Teleported to the waypoint!"},
            color = {190, 0, 0} -- Red message
        })
    else
        -- Error 
        TriggerEvent('chat:addMessage', {
            args = {"Error", "No waypoint set!"},
            color = {190, 0, 0} -- Red message
        })
    end
end, false)
