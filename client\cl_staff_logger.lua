-- Client-side Staff Logger
-- Handles logging for client-side commands

-- Hook into client-side RegisterCommand to log usage
local originalRegisterCommand = RegisterCommand
RegisterCommand = function(commandName, handler, restricted)
    -- Create wrapped handler that logs command usage
    local wrappedHandler = function(source, args, rawCommand)
        -- Send log to server
        TriggerServerEvent('stafflogger:logCommand', commandName, args)
        
        -- Call original handler
        return handler(source, args, rawCommand)
    end
    
    -- Register with wrapped handler
    return originalRegisterCommand(commandName, wrappedHandler, restricted)
end

-- Manual logging for specific commands that need special tracking
local function logClientCommand(command, args)
    TriggerServerEvent('stafflogger:logCommand', command, args or {})
end

-- Override specific functions to ensure logging
local originalHealPlayer = HealPlayer
if originalHealPlayer then
    HealPlayer = function()
        logClientCommand('heal', {})
        return originalHealPlayer()
    end
end

local originalGiveArmour = GiveArmour
if originalGiveArmour then
    GiveArmour = function()
        logClientCommand('armour', {})
        return originalGiveArmour()
    end
end

-- Log teleport commands
local originalTeleportPlayerTo = teleportPlayerTo
if originalTeleportPlayerTo then
    teleportPlayerTo = function(locationKey)
        logClientCommand('teleport', {locationKey})
        return originalTeleportPlayerTo(locationKey)
    end
end

-- Add logging for any other client-side commands that might be missed
RegisterNetEvent('stafflogger:forceLog')
AddEventHandler('stafflogger:forceLog', function(command, args)
    logClientCommand(command, args)
end)

print("[StaffLogger] Client-side logging hooks initialized")
