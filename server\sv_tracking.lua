RegisterNetEvent("custom:checkPlayerBucket", function()
    local playerId = source
    local bucket = GetPlayerRoutingBucket(playerId)
    TriggerClientEvent("custom:receivePlayerBucket", playerId, bucket)
end)

RegisterNetEvent("custom:verifyPlayerBucket", function()
    local playerId = source
    local bucket = GetPlayerRoutingBucket(playerId)
    TriggerClientEvent("custom:verifyBucketResponse", playerId, bucket)
end)