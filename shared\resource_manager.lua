-- Resource Manager for X Addons
-- Handles proper cleanup and resource management

ResourceManager = {}

-- Track all created resources for cleanup
local trackedBlips = {}
local trackedEntities = {}
local trackedThreads = {}
local trackedEvents = {}

-- Resource tracking functions
function ResourceManager.TrackBlip(blip, name)
    if blip and DoesBlipExist(blip) then
        trackedBlips[blip] = {
            name = name or "Unknown",
            created = GetGameTimer()
        }
        return blip
    end
    return nil
end

function ResourceManager.TrackEntity(entity, name)
    if entity and DoesEntityExist(entity) then
        trackedEntities[entity] = {
            name = name or "Unknown",
            created = GetGameTimer(),
            type = GetEntityType(entity)
        }
        return entity
    end
    return nil
end

function ResourceManager.TrackThread(threadId, name)
    trackedThreads[threadId] = {
        name = name or "Unknown",
        created = GetGameTimer()
    }
    return threadId
end

function ResourceManager.TrackEvent(eventName, handler)
    if not trackedEvents[eventName] then
        trackedEvents[eventName] = {}
    end
    table.insert(trackedEvents[eventName], handler)
    return handler
end

-- Cleanup functions
function ResourceManager.CleanupBlip(blip)
    if blip and DoesBlipExist(blip) then
        RemoveBlip(blip)
        trackedBlips[blip] = nil
        return true
    end
    return false
end

function ResourceManager.CleanupEntity(entity)
    if entity and DoesEntityExist(entity) then
        local entityType = GetEntityType(entity)
        
        if entityType == 1 then -- Ped
            DeletePed(entity)
        elseif entityType == 2 then -- Vehicle
            DeleteVehicle(entity)
        else -- Object
            DeleteEntity(entity)
        end
        
        trackedEntities[entity] = nil
        return true
    end
    return false
end

-- Cleanup all tracked resources
function ResourceManager.CleanupAll()
    local cleaned = {blips = 0, entities = 0}
    
    -- Cleanup blips
    for blip, data in pairs(trackedBlips) do
        if ResourceManager.CleanupBlip(blip) then
            cleaned.blips = cleaned.blips + 1
        end
    end
    
    -- Cleanup entities
    for entity, data in pairs(trackedEntities) do
        if ResourceManager.CleanupEntity(entity) then
            cleaned.entities = cleaned.entities + 1
        end
    end
    
    -- Clear tracking tables
    trackedBlips = {}
    trackedEntities = {}
    
    return cleaned
end

-- Get resource statistics
function ResourceManager.GetStats()
    local stats = {
        blips = 0,
        entities = 0,
        threads = 0,
        events = 0
    }
    
    for blip, _ in pairs(trackedBlips) do
        if DoesBlipExist(blip) then
            stats.blips = stats.blips + 1
        else
            trackedBlips[blip] = nil -- Clean up invalid references
        end
    end
    
    for entity, _ in pairs(trackedEntities) do
        if DoesEntityExist(entity) then
            stats.entities = stats.entities + 1
        else
            trackedEntities[entity] = nil -- Clean up invalid references
        end
    end
    
    stats.threads = #trackedThreads
    
    for eventName, handlers in pairs(trackedEvents) do
        stats.events = stats.events + #handlers
    end
    
    return stats
end

-- Memory optimization
function ResourceManager.OptimizeMemory()
    -- Force garbage collection
    collectgarbage("collect")
    
    -- Clean up invalid references
    for blip, _ in pairs(trackedBlips) do
        if not DoesBlipExist(blip) then
            trackedBlips[blip] = nil
        end
    end
    
    for entity, _ in pairs(trackedEntities) do
        if not DoesEntityExist(entity) then
            trackedEntities[entity] = nil
        end
    end
end

-- Automatic cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        local cleaned = ResourceManager.CleanupAll()
        print(string.format("[ResourceManager] Cleaned up %d blips and %d entities on resource stop", 
            cleaned.blips, cleaned.entities))
    end
end)

-- Periodic memory optimization (every 5 minutes)
if IsDuplicityVersion() then -- Server side
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(300000) -- 5 minutes
            ResourceManager.OptimizeMemory()
        end
    end)
else -- Client side
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(300000) -- 5 minutes
            ResourceManager.OptimizeMemory()
        end
    end)
end

return ResourceManager
