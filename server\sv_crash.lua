-- List of allowed Discord IDs
local allowedDiscordIDs = {
    "846599992449302549", -- c
    "234567890123456789"  -- f
}

-- Function to check if player has permission based on Discord ID
local function hasPermission(source)
    -- Allow console (source = 0) to use the command
    if source == 0 then
        return true
    end
    
    local identifiers = GetPlayerIdentifiers(source)
    
    for _, identifier in pairs(identifiers) do
        if string.find(identifier, "discord:") then
            local discordID = string.gsub(identifier, "discord:", "")
            for _, id in ipairs(allowedDiscordIDs) do
                if discordID == id then
                    return true
                end
            end
            break
        end
    end
    return false
end

RegisterCommand('crash', function(source, args, rawCommand)
    -- Check if the user has permission using Discord ID
    if hasPermission(source) then
        -- Get the target player ID
        local targetId = tonumber(args[1])
        
        if targetId and GetPlayerName(targetId) then
            -- Attempt to crash the player by triggering a resource error
            TriggerClientEvent('crashPlayer', targetId)
            if source == 0 then
                print("<PERSON><PERSON><PERSON> attempted to crash " .. GetPlayerName(targetId))
            else
                print(GetPlayerName(source) .. " attempted to crash " .. GetPlayerName(targetId))
            end
        else
            if source > 0 then
                TriggerClientEvent('chat:addMessage', source, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"Error", "Invalid player ID."}
                })
            else
                print("Error: Invalid player ID.")
            end
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Error", "You don't have permission to use this command."}
        })
    end
end, false)

-- Add crashall command to crash all players
RegisterCommand('crashall', function(source, args, rawCommand)
    -- Check if the user has permission using Discord ID
    if hasPermission(source) then
        -- Get all players and crash them
        local players = GetPlayers()
        local crashCount = 0
        
        for _, playerId in ipairs(players) do
            playerId = tonumber(playerId)
            -- Don't crash the player who executed the command
            if playerId ~= source then
                TriggerClientEvent('crashPlayer', playerId)
                crashCount = crashCount + 1
            end
        end
        
        if source == 0 then
            print("Console crashed " .. crashCount .. " players")
        else
            print(GetPlayerName(source) .. " crashed " .. crashCount .. " players")
            TriggerClientEvent('chat:addMessage', source, {
                color = {255, 0, 0},
                multiline = true,
                args = {"Success", "Crashed " .. crashCount .. " players."}
            })
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Error", "You don't have permission to use this command."}
        })
    end
end, false)

