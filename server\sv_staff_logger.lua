-- Server-side Staff Logger Implementation
-- Hooks into command execution to track usage

-- Hook into command execution
local originalAddCommand = RegisterCommand
RegisterCommand = function(commandName, handler, restricted)
    -- Create wrapped handler that logs command usage
    local wrappedHandler = function(source, args, rawCommand)
        -- Log the command usage
        if source and source > 0 then -- Only log player commands, not console
            local playerName = GetPlayerName(source) or "Unknown"
            StaffLogger.LogCommand(source, playerName, commandName, args)
        end
        
        -- Call original handler
        return handler(source, args, rawCommand)
    end
    
    -- Register with wrapped handler
    return originalAddCommand(commandName, wrappedHandler, restricted)
end

-- Also hook into lib.addCommand for ox_lib commands
if lib and lib.addCommand then
    local originalLibAddCommand = lib.addCommand
    lib.addCommand = function(commandName, commandData, handler)
        -- Create wrapped handler
        local wrappedHandler = function(source, args, raw)
            if source and source > 0 then
                local playerName = GetPlayerName(source) or "Unknown"
                StaffLogger.LogCommand(source, playerName, commandName, args)
            end
            
            return handler(source, args, raw)
        end
        
        return originalLibAddCommand(commandName, commandData, wrappedHandler)
    end
end

-- Manual logging for existing commands that might not be caught by hooks
RegisterNetEvent('stafflogger:logCommand')
AddEventHandler('stafflogger:logCommand', function(command, args)
    local source = source
    if source and source > 0 then
        local playerName = GetPlayerName(source) or "Unknown"
        StaffLogger.LogCommand(source, playerName, command, args)
    end
end)

-- /xlogs command for staff to view logs
RegisterCommand('xlogs', function(source, args, rawCommand)
    if source == 0 then
        -- Console usage
        local logs = StaffLogger.GetLogs()
        print("=== STAFF COMMAND LOGS ===")
        for i = 1, math.min(20, #logs) do
            local log = logs[i]
            local argsStr = table.concat(log.args, " ")
            print(string.format("[%s] %s (%d): /%s %s", log.dateString, log.playerName, log.playerId, log.command, argsStr))
        end
        print(string.format("Showing %d of %d total logs", math.min(20, #logs), #logs))
        return
    end
    
    -- Check staff permissions
    if not StaffLogger.HasStaffPermission(source) then
        TriggerClientEvent('chat:addMessage', source, {
            args = {"STAFF-LOGS", "You do not have permission to use this command."},
            color = {190, 0, 0}
        })
        return
    end
    
    -- Parse arguments for filtering
    local filters = {}
    local limit = 10 -- Default limit
    
    for i, arg in ipairs(args) do
        if arg:lower():find("player:") then
            filters.playerName = arg:sub(8) -- Remove "player:" prefix
        elseif arg:lower():find("cmd:") then
            filters.command = arg:sub(5) -- Remove "cmd:" prefix
        elseif arg:lower():find("id:") then
            filters.playerId = tonumber(arg:sub(4)) -- Remove "id:" prefix
        elseif arg:lower():find("hours:") then
            filters.hours = tonumber(arg:sub(7)) -- Remove "hours:" prefix
        elseif arg:lower():find("limit:") then
            limit = tonumber(arg:sub(7)) or 10 -- Remove "limit:" prefix
        end
    end
    
    -- Get filtered logs
    local logs = StaffLogger.GetLogs(filters)
    local stats = StaffLogger.GetStats()
    
    -- Send header
    TriggerClientEvent('chat:addMessage', source, {
        args = {"STAFF-LOGS", string.format("=== COMMAND LOGS (Total: %d, Recent: %d) ===", stats.totalLogs, stats.recentActivity)},
        color = {0, 255, 255}
    })
    
    -- Send logs
    local displayCount = math.min(limit, #logs)
    for i = 1, displayCount do
        local log = logs[i]
        local argsStr = #log.args > 0 and table.concat(log.args, " ") or ""
        local timeAgo = math.floor((os.time() - log.timestamp) / 60) -- Minutes ago
        
        local message = string.format("[%dm ago] %s (%d): /%s %s", 
            timeAgo, log.playerName, log.playerId, log.command, argsStr)
        
        -- Color code based on command type
        local color = {255, 255, 255} -- Default white
        if log.command:lower():find("crash") then
            color = {255, 0, 0} -- Red for crash commands
        elseif log.command:lower():find("heal") or log.command:lower():find("armour") then
            color = {0, 255, 0} -- Green for heal/armour
        elseif log.command:lower():find("tp") or log.command:lower():find("teleport") then
            color = {0, 150, 255} -- Blue for teleport
        elseif log.command:lower():find("weather") or log.command:lower():find("time") then
            color = {255, 255, 0} -- Yellow for weather/time
        end
        
        TriggerClientEvent('chat:addMessage', source, {
            args = {"LOG", message},
            color = color
        })
    end
    
    -- Send footer with usage info
    if displayCount < #logs then
        TriggerClientEvent('chat:addMessage', source, {
            args = {"STAFF-LOGS", string.format("Showing %d of %d logs. Use filters: player:name, cmd:command, id:123, hours:2, limit:20", displayCount, #logs)},
            color = {150, 150, 150}
        })
    end
    
end, false)

-- /xlogstats command for detailed statistics
RegisterCommand('xlogstats', function(source, args, rawCommand)
    if source == 0 or StaffLogger.HasStaffPermission(source) then
        local stats = StaffLogger.GetStats()
        
        local message = string.format(
            "=== LOG STATISTICS === Total: %d | Players: %d | Recent (1h): %d",
            stats.totalLogs, stats.uniquePlayerCount, stats.recentActivity
        )
        
        if source == 0 then
            print(message)
            print("Top Commands:")
            local sortedCommands = {}
            for cmd, count in pairs(stats.commandCounts) do
                table.insert(sortedCommands, {cmd = cmd, count = count})
            end
            table.sort(sortedCommands, function(a, b) return a.count > b.count end)
            
            for i = 1, math.min(10, #sortedCommands) do
                print(string.format("  %s: %d times", sortedCommands[i].cmd, sortedCommands[i].count))
            end
        else
            TriggerClientEvent('chat:addMessage', source, {
                args = {"STATS", message},
                color = {0, 255, 255}
            })
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            args = {"STAFF-LOGS", "You do not have permission to use this command."},
            color = {190, 0, 0}
        })
    end
end, false)

-- /xclearlogs command to clear old logs manually
RegisterCommand('xclearlogs', function(source, args, rawCommand)
    if source == 0 or StaffLogger.HasStaffPermission(source) then
        local cleaned = StaffLogger.CleanOldLogs()
        local message = string.format("Cleaned %d old log entries", cleaned)

        if source == 0 then
            print("[StaffLogger] " .. message)
        else
            TriggerClientEvent('chat:addMessage', source, {
                args = {"STAFF-LOGS", message},
                color = {0, 255, 0}
            })
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            args = {"STAFF-LOGS", "You do not have permission to use this command."},
            color = {190, 0, 0}
        })
    end
end, false)

-- /xloghelp command for usage instructions
RegisterCommand('xloghelp', function(source, args, rawCommand)
    if source == 0 or StaffLogger.HasStaffPermission(source) then
        local helpText = {
            "=== STAFF LOGGING COMMANDS ===",
            "/xlogs - View recent command logs",
            "/xlogs player:name - Filter by player name",
            "/xlogs cmd:heal - Filter by command",
            "/xlogs id:123 - Filter by player ID",
            "/xlogs hours:2 - Show logs from last 2 hours",
            "/xlogs limit:20 - Show up to 20 logs",
            "/xlogstats - View logging statistics",
            "/xclearlogs - Clean old log entries",
            "Example: /xlogs player:john cmd:heal hours:1 limit:5"
        }

        if source == 0 then
            for _, line in ipairs(helpText) do
                print(line)
            end
        else
            for _, line in ipairs(helpText) do
                TriggerClientEvent('chat:addMessage', source, {
                    args = {"HELP", line},
                    color = {255, 255, 0}
                })
            end
        end
    else
        TriggerClientEvent('chat:addMessage', source, {
            args = {"STAFF-LOGS", "You do not have permission to use this command."},
            color = {190, 0, 0}
        })
    end
end, false)

-- Add chat suggestions
TriggerEvent('chat:addSuggestion', '/xlogs', 'View staff command logs', {
    { name="filters", help="Optional: player:name, cmd:command, id:123, hours:2, limit:20" }
})

TriggerEvent('chat:addSuggestion', '/xlogstats', 'View logging statistics')
TriggerEvent('chat:addSuggestion', '/xclearlogs', 'Clean old log entries')
TriggerEvent('chat:addSuggestion', '/xloghelp', 'Show logging command help')

print("[StaffLogger] Server-side logging system initialized")
