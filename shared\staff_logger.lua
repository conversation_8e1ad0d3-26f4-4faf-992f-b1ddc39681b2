-- Staff Logger System for X Addons
-- Tracks all command usage for staff monitoring

StaffLogger = {}

-- Configuration
local MAX_LOGS = 500 -- Maximum number of logs to keep in memory
local LOG_RETENTION_HOURS = 24 -- How long to keep logs (in hours)

-- Storage
local commandLogs = {}
local logIndex = 1

-- Staff permission check (using existing Discord ID system from sv_logs.lua)
local authorizedDiscordIds = {
    "846599992449302549", -- cruzy
    "discord_id_2", -- Add more Discord IDs here
    "discord_id_3", -- Add more Discord IDs here
    "discord_id_4", -- Add more Discord IDs here
}

-- Commands to track
local trackedCommands = {
    -- Player utility commands
    "heal", "h", "armour", "a", "resetme",
    
    -- Teleport commands
    "tpm", "tpall", "teleport", "tracking", "spawn",
    
    -- Admin commands
    "announce", "clearall", "cleanup", "dv", "setped",
    "toggleredx", "togglesoftdv", "togglefulldv",
    
    -- Weather/Time commands
    "weather", "time", "freezetime", "blackout", "morning", "noon", "evening", "night",
    
    -- Greenzone commands
    "setzone", "clearzone",
    
    -- Bucket commands
    "joinbucket", "checkbucket",
    
    -- Weapon commands
    "scope", "extended", "grip", "range",
    
    -- Debug commands
    "xperf", "xoptimize", "xcleanup",
    
    -- Crash commands (high priority)
    "crash", "crashall",
    
    -- Other utility
    "logs", "coords"
}

-- Log entry structure
function StaffLogger.CreateLogEntry(playerId, playerName, command, args, timestamp)
    return {
        id = logIndex,
        playerId = playerId,
        playerName = playerName,
        command = command,
        args = args or {},
        timestamp = timestamp or os.time(),
        dateString = os.date("%Y-%m-%d %H:%M:%S", timestamp or os.time())
    }
end

-- Add a log entry
function StaffLogger.LogCommand(playerId, playerName, command, args)
    -- Check if logging is enabled
    if Config and Config.StaffLogging == false then
        return
    end

    -- Only log tracked commands
    local shouldLog = false
    for _, trackedCmd in ipairs(trackedCommands) do
        if string.lower(command) == string.lower(trackedCmd) then
            shouldLog = true
            break
        end
    end

    if not shouldLog then
        return
    end
    
    local logEntry = StaffLogger.CreateLogEntry(playerId, playerName, command, args)
    
    -- Add to logs
    table.insert(commandLogs, logEntry)
    logIndex = logIndex + 1
    
    -- Maintain max logs limit
    if #commandLogs > MAX_LOGS then
        table.remove(commandLogs, 1)
    end
    
    -- Print to server console for immediate visibility (if enabled)
    if Config and Config.LogToConsole ~= false then
        local argsStr = args and table.concat(args, " ") or ""
        print(string.format("[STAFF-LOG] %s (%d) used /%s %s", playerName, playerId, command, argsStr))
    end
    
    return logEntry
end

-- Get logs with optional filtering
function StaffLogger.GetLogs(filters)
    filters = filters or {}
    local filteredLogs = {}
    local currentTime = os.time()
    
    for _, log in ipairs(commandLogs) do
        -- Check retention time
        if (currentTime - log.timestamp) <= (LOG_RETENTION_HOURS * 3600) then
            local include = true
            
            -- Apply filters
            if filters.playerId and log.playerId ~= filters.playerId then
                include = false
            end
            
            if filters.command and string.lower(log.command) ~= string.lower(filters.command) then
                include = false
            end
            
            if filters.playerName and not string.find(string.lower(log.playerName), string.lower(filters.playerName)) then
                include = false
            end
            
            if filters.hours and (currentTime - log.timestamp) > (filters.hours * 3600) then
                include = false
            end
            
            if include then
                table.insert(filteredLogs, log)
            end
        end
    end
    
    -- Sort by timestamp (newest first)
    table.sort(filteredLogs, function(a, b) return a.timestamp > b.timestamp end)
    
    return filteredLogs
end

-- Clean old logs
function StaffLogger.CleanOldLogs()
    local currentTime = os.time()
    local cleaned = 0
    
    for i = #commandLogs, 1, -1 do
        if (currentTime - commandLogs[i].timestamp) > (LOG_RETENTION_HOURS * 3600) then
            table.remove(commandLogs, i)
            cleaned = cleaned + 1
        end
    end
    
    return cleaned
end

-- Check if player has staff permissions
function StaffLogger.HasStaffPermission(playerId)
    local identifiers = GetPlayerIdentifiers(playerId)
    local discordId = nil
    
    -- Find Discord ID
    for _, id in ipairs(identifiers) do
        if string.sub(id, 1, 8) == "discord:" then
            discordId = string.sub(id, 9)
            break
        end
    end
    
    if not discordId then
        return false
    end
    
    -- Check authorization
    for _, authorizedId in ipairs(authorizedDiscordIds) do
        if discordId == authorizedId then
            return true
        end
    end
    
    return false
end

-- Get statistics
function StaffLogger.GetStats()
    local stats = {
        totalLogs = #commandLogs,
        uniquePlayers = {},
        commandCounts = {},
        recentActivity = 0
    }
    
    local currentTime = os.time()
    local oneHourAgo = currentTime - 3600
    
    for _, log in ipairs(commandLogs) do
        -- Count unique players
        stats.uniquePlayers[log.playerId] = (stats.uniquePlayers[log.playerId] or 0) + 1
        
        -- Count commands
        stats.commandCounts[log.command] = (stats.commandCounts[log.command] or 0) + 1
        
        -- Count recent activity (last hour)
        if log.timestamp >= oneHourAgo then
            stats.recentActivity = stats.recentActivity + 1
        end
    end
    
    -- Convert unique players to count
    local playerCount = 0
    for _ in pairs(stats.uniquePlayers) do
        playerCount = playerCount + 1
    end
    stats.uniquePlayerCount = playerCount
    
    return stats
end

-- Periodic cleanup (run every hour)
if IsDuplicityVersion() then -- Server side only
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(3600000) -- 1 hour
            local cleaned = StaffLogger.CleanOldLogs()
            if cleaned > 0 then
                print(string.format("[StaffLogger] Cleaned %d old log entries", cleaned))
            end
        end
    end)
end

return StaffLogger
